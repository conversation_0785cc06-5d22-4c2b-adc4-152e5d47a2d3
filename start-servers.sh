#!/bin/bash

# =============================================================================
# PropBolt Brain Server Startup Script
# =============================================================================
# This script starts both frontend and backend servers with SSL enabled
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting PropBolt Brain Servers${NC}"
echo "=================================="

# Function to cleanup background processes
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down servers...${NC}"
    jobs -p | xargs -r kill
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Check if SSL certificates exist
if [ ! -f "certs/server.crt" ] || [ ! -f "certs/server.key" ]; then
    echo -e "${YELLOW}⚠️  SSL certificates not found. Setting up SSL...${NC}"
    ./setup-ssl.sh
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
    sudo npm install
fi

# Build the Go backend
echo -e "${YELLOW}🔨 Building Go backend...${NC}"
go mod tidy
go build -o propbolt-brain

# Set environment variables for local development
export PORT=8080
export NODE_ENV=development
export NEXT_PUBLIC_API_BASE_URL=https://localhost:8080

# Update .env.local for local development
if [ -f ".env.local" ]; then
    # Update API URL for local development
    sed -i.bak 's|NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=https://localhost:8080|' .env.local
    sed -i.bak 's|BETTER_AUTH_URL=.*|BETTER_AUTH_URL=https://localhost:3000|' .env.local
    sed -i.bak 's|NEXT_PUBLIC_BETTER_AUTH_URL=.*|NEXT_PUBLIC_BETTER_AUTH_URL=https://localhost:3000|' .env.local
fi

# Start the Go backend with SSL
echo -e "${YELLOW}🔧 Starting Go backend server on https://localhost:8080...${NC}"
PORT=8080 ./propbolt-brain &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Test backend health
if curl -k -s https://localhost:8080/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend server started successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Backend not responding on HTTPS, trying HTTP...${NC}"
    if curl -s http://localhost:8080/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend server started on HTTP${NC}"
        export NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
    else
        echo -e "${RED}❌ Backend server failed to start${NC}"
        exit 1
    fi
fi

# Start the Next.js frontend with SSL
echo -e "${YELLOW}🔧 Starting Next.js frontend server on https://localhost:3000...${NC}"
npm run dev &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 5

echo -e "${GREEN}🎉 Both servers are running!${NC}"
echo ""
echo -e "${BLUE}📊 Server Status:${NC}"
echo "  🔗 Frontend: https://localhost:3000"
echo "  🔗 Backend:  https://localhost:8080 (or http://localhost:8080)"
echo "  🔗 API Docs: https://localhost:8080/ (health check)"
echo ""
echo -e "${BLUE}🔐 Authentication:${NC}"
echo "  📝 Login page: https://localhost:3000/login"
echo "  🛡️  Protected routes: All pages except /login"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo "  • Use Ctrl+C to stop both servers"
echo "  • Check browser console for any SSL certificate warnings"
echo "  • Add localhost certificates to your browser if needed"
echo ""
echo -e "${GREEN}✨ PropBolt Brain is ready for development!${NC}"

# Keep script running and show logs
echo -e "${BLUE}📋 Server logs (Ctrl+C to stop):${NC}"
echo "=================================="

# Wait for background processes
wait
