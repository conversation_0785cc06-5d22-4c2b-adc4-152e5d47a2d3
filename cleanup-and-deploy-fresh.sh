#!/bin/bash

# =============================================================================
# PropBolt Brain - Clean Deployment Script
# =============================================================================
# This script cleans up old resources and deploys fresh infrastructure
# with auto-scaling compute instances and load balancing
# =============================================================================

set -e

# Configuration
PROJECT_ID="gold-braid-458901-v2"
PROJECT_NUMBER="456078002475"
REGION="us-east1"
ZONE="us-east1-b"
SERVICE_NAME="brain-api"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧹 PropBolt Brain - Clean Deployment${NC}"
echo "=================================================="
echo "Project ID: $PROJECT_ID"
echo "Project Number: $PROJECT_NUMBER"
echo "Region: $REGION"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Google Cloud CLI not found. Please install gcloud first.${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project...${NC}"
sudo gcloud config set project $PROJECT_ID

# =============================================================================
# Step 1: Cleanup Old Resources
# =============================================================================
echo -e "${YELLOW}🧹 Cleaning up old resources...${NC}"

# Delete old Cloud Run services
echo "Deleting old Cloud Run services..."
sudo gcloud run services list --region=$REGION --format="value(metadata.name)" | while read service; do
    if [ ! -z "$service" ]; then
        echo "Deleting Cloud Run service: $service"
        sudo gcloud run services delete $service --region=$REGION --quiet || echo "Service $service may not exist"
    fi
done

# Delete old Cloud SQL instances
echo "Deleting old Cloud SQL instances..."
sudo gcloud sql instances list --format="value(name)" | while read instance; do
    if [ ! -z "$instance" ]; then
        echo "Deleting Cloud SQL instance: $instance"
        # Remove deletion protection first
        sudo gcloud sql instances patch $instance --no-deletion-protection --quiet || true
        sudo gcloud sql instances delete $instance --quiet || echo "Instance $instance may not exist"
    fi
done

# Delete old Redis instances
echo "Deleting old Redis instances..."
sudo gcloud redis instances list --region=$REGION --format="value(name)" | while read instance; do
    if [ ! -z "$instance" ]; then
        echo "Deleting Redis instance: $instance"
        sudo gcloud redis instances delete $instance --region=$REGION --quiet || echo "Instance $instance may not exist"
    fi
done

# Delete old load balancers and networking
echo "Deleting old load balancer components..."
sudo gcloud compute forwarding-rules list --global --format="value(name)" | while read rule; do
    if [ ! -z "$rule" ]; then
        echo "Deleting forwarding rule: $rule"
        sudo gcloud compute forwarding-rules delete $rule --global --quiet || true
    fi
done

sudo gcloud compute target-https-proxies list --format="value(name)" | while read proxy; do
    if [ ! -z "$proxy" ]; then
        echo "Deleting HTTPS proxy: $proxy"
        sudo gcloud compute target-https-proxies delete $proxy --quiet || true
    fi
done

sudo gcloud compute url-maps list --format="value(name)" | while read urlmap; do
    if [ ! -z "$urlmap" ]; then
        echo "Deleting URL map: $urlmap"
        sudo gcloud compute url-maps delete $urlmap --quiet || true
    fi
done

sudo gcloud compute backend-services list --global --format="value(name)" | while read backend; do
    if [ ! -z "$backend" ]; then
        echo "Deleting backend service: $backend"
        sudo gcloud compute backend-services delete $backend --global --quiet || true
    fi
done

# Delete old SSL certificates
echo "Deleting old SSL certificates..."
sudo gcloud compute ssl-certificates list --format="value(name)" | while read cert; do
    if [ ! -z "$cert" ]; then
        echo "Deleting SSL certificate: $cert"
        sudo gcloud compute ssl-certificates delete $cert --quiet || true
    fi
done

# Delete old storage buckets
echo "Deleting old storage buckets..."
sudo gsutil ls | while read bucket; do
    if [[ $bucket == *"brain"* ]] || [[ $bucket == *"propbolt"* ]]; then
        echo "Deleting bucket: $bucket"
        sudo gsutil -m rm -r $bucket || true
    fi
done

echo -e "${GREEN}✅ Cleanup completed!${NC}"

# =============================================================================
# Step 2: Enable Required APIs
# =============================================================================
echo -e "${YELLOW}🔧 Enabling required APIs...${NC}"

sudo gcloud services enable cloudbuild.googleapis.com
sudo gcloud services enable run.googleapis.com
sudo gcloud services enable sqladmin.googleapis.com
sudo gcloud services enable storage.googleapis.com
sudo gcloud services enable logging.googleapis.com
sudo gcloud services enable monitoring.googleapis.com
sudo gcloud services enable redis.googleapis.com
sudo gcloud services enable compute.googleapis.com
sudo gcloud services enable certificatemanager.googleapis.com
sudo gcloud services enable container.googleapis.com
sudo gcloud services enable deploymentmanager.googleapis.com
sudo gcloud services enable cloudresourcemanager.googleapis.com

echo -e "${GREEN}✅ APIs enabled!${NC}"

# =============================================================================
# Step 3: Create Fresh Infrastructure
# =============================================================================
echo -e "${YELLOW}🏗️ Creating fresh infrastructure...${NC}"

# Create new Cloud SQL instance with high availability
echo "Creating new Cloud SQL PostgreSQL instance..."
sudo gcloud sql instances create propbolt-brain-db \
    --database-version=POSTGRES_15 \
    --tier=db-custom-4-8192 \
    --region=$REGION \
    --availability-type=REGIONAL \
    --storage-type=SSD \
    --storage-size=200GB \
    --storage-auto-increase \
    --backup-start-time=02:00 \
    --maintenance-window-day=SUN \
    --maintenance-window-hour=03 \
    --enable-point-in-time-recovery \
    --retained-backups-count=30 \
    --retained-transaction-log-days=7

# Create database and user
sudo gcloud sql databases create brain_propbolt_prod --instance=propbolt-brain-db
sudo gcloud sql users create brain_admin \
    --instance=propbolt-brain-db \
    --password=$(openssl rand -base64 32)

# Create read replicas for load distribution
sudo gcloud sql instances create propbolt-brain-db-replica-east \
    --master-instance-name=propbolt-brain-db \
    --region=$REGION \
    --tier=db-custom-2-4096 \
    --replica-type=READ

sudo gcloud sql instances create propbolt-brain-db-replica-west \
    --master-instance-name=propbolt-brain-db \
    --region=us-west1 \
    --tier=db-custom-2-4096 \
    --replica-type=READ

# Create Redis Enterprise instance
echo "Creating Redis instance..."
sudo gcloud redis instances create propbolt-brain-cache \
    --size=5 \
    --region=$REGION \
    --redis-version=redis_7_0 \
    --tier=standard_ha

# Create storage bucket
echo "Creating storage bucket..."
sudo gsutil mb -p $PROJECT_ID -c STANDARD -l $REGION gs://propbolt-brain-storage-$(date +%s)

echo -e "${GREEN}✅ Infrastructure created!${NC}"

echo -e "${BLUE}🎉 Fresh deployment preparation completed!${NC}"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Run: sudo ./deploy.sh (to deploy the application)"
echo "2. Run: sudo ./setup-production-ssl.sh (to set up SSL)"
echo "3. Run: sudo ./setup-google-oauth.sh (to configure OAuth)"
echo ""
echo -e "${GREEN}✨ Ready for fresh deployment!${NC}"
