package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"propbolt/realestateapi"
)

// ProxyHandler handles all RealEstateAPI proxy requests
type ProxyHandler struct {
	client *realestateapi.Client
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler() *ProxyHandler {
	return &ProxyHandler{
		client: realestateapi.NewClient(),
	}
}

// writeJSONResponse writes a JSON response
func (h *ProxyHandler) writeJSONResponse(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if err := json.NewEncoder(w).Encode(data); err != nil {
		http.Error(w, fmt.Sprintf("Error encoding response: %v", err), http.StatusInternalServerError)
		return
	}
}

// writeErrorResponse writes a structured error response
func (h *ProxyHandler) writeErrorResponse(w http.ResponseWriter, message string, code string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	errorResponse := map[string]interface{}{
		"error": map[string]interface{}{
			"message": message,
			"code":    code,
			"details": map[string]interface{}{
				"timestamp": fmt.Sprintf("%d", statusCode),
			},
		},
	}

	json.NewEncoder(w).Encode(errorResponse)
}

// AutoCompleteHandler handles autocomplete requests
func (h *ProxyHandler) AutoCompleteHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Input string `json:"input"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Input == "" {
		h.writeErrorResponse(w, "Input parameter is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.AutoComplete(request.Input)
	if err != nil {
		log.Printf("AutoComplete API error: %v", err)
		h.writeErrorResponse(w, "Failed to get autocomplete suggestions", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertyMappingHandler handles property mapping requests
func (h *ProxyHandler) PropertyMappingHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Query interface{} `json:"query"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	response, err := h.client.PropertyMapping(request.Query)
	if err != nil {
		log.Printf("PropertyMapping API error: %v", err)
		h.writeErrorResponse(w, "Failed to get property mapping data", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertyDetailHandler handles property detail requests
func (h *ProxyHandler) PropertyDetailHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Address    string `json:"address"`
		PropertyID string `json:"propertyId"`
		Comps      bool   `json:"comps"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Address == "" && request.PropertyID == "" {
		h.writeErrorResponse(w, "Either address or propertyId is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.PropertyDetail(request.Address, request.PropertyID, request.Comps)
	if err != nil {
		log.Printf("PropertyDetail API error: %v", err)
		h.writeErrorResponse(w, "Failed to get property details", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertyDetailBulkHandler handles bulk property detail requests
func (h *ProxyHandler) PropertyDetailBulkHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		PropertyIDs []string `json:"propertyIds"`
		Comps       bool     `json:"comps"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if len(request.PropertyIDs) == 0 {
		h.writeErrorResponse(w, "PropertyIDs array is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	if len(request.PropertyIDs) > 1000 {
		h.writeErrorResponse(w, "Maximum 1000 property IDs allowed", "INVALID_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.PropertyDetailBulk(request.PropertyIDs, request.Comps)
	if err != nil {
		log.Printf("PropertyDetailBulk API error: %v", err)
		h.writeErrorResponse(w, "Failed to get bulk property details", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertySearchHandler handles property search requests
func (h *ProxyHandler) PropertySearchHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Query   interface{} `json:"query"`
		Limit   int         `json:"limit"`
		Offset  int         `json:"offset"`
		IDsOnly bool        `json:"ids_only"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	// Set defaults
	if request.Limit == 0 {
		request.Limit = 100
	}

	response, err := h.client.PropertySearch(request.Query, request.Limit, request.Offset, request.IDsOnly)
	if err != nil {
		log.Printf("PropertySearch API error: %v", err)
		h.writeErrorResponse(w, "Failed to search properties", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// InvoluntaryLiensHandler handles involuntary liens requests
func (h *ProxyHandler) InvoluntaryLiensHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Address    string `json:"address"`
		PropertyID string `json:"propertyId"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Address == "" && request.PropertyID == "" {
		h.writeErrorResponse(w, "Either address or propertyId is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.InvoluntaryLiens(request.Address, request.PropertyID)
	if err != nil {
		log.Printf("InvoluntaryLiens API error: %v", err)
		h.writeErrorResponse(w, "Failed to get lien information", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertyCompsV3Handler handles property comps v3 requests
func (h *ProxyHandler) PropertyCompsV3Handler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Address           string                 `json:"address"`
		CustomParameters  map[string]interface{} `json:"customParameters"`
		BoostParameters   map[string]interface{} `json:"boostParameters"`
		MaxComps          int                    `json:"maxComps"`
		RadiusMiles       float64                `json:"radiusMiles"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Address == "" {
		h.writeErrorResponse(w, "Address is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.PropertyCompsV3(request.Address, request.CustomParameters, request.BoostParameters, request.MaxComps, request.RadiusMiles)
	if err != nil {
		log.Printf("PropertyCompsV3 API error: %v", err)
		h.writeErrorResponse(w, "Failed to get property comparables", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// PropertyCompsV2Handler handles property comps v2 requests
func (h *ProxyHandler) PropertyCompsV2Handler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Address     string  `json:"address"`
		MaxComps    int     `json:"maxComps"`
		RadiusMiles float64 `json:"radiusMiles"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Address == "" {
		h.writeErrorResponse(w, "Address is required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.PropertyCompsV2(request.Address, request.MaxComps, request.RadiusMiles)
	if err != nil {
		log.Printf("PropertyCompsV2 API error: %v", err)
		h.writeErrorResponse(w, "Failed to get property comparables", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}

// SkipTraceHandler handles skip trace requests
func (h *ProxyHandler) SkipTraceHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		h.writeErrorResponse(w, "Method not allowed", "METHOD_NOT_ALLOWED", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		FirstName string `json:"first_name"`
		LastName  string `json:"last_name"`
		Address   string `json:"address"`
		City      string `json:"city"`
		State     string `json:"state"`
		Zip       string `json:"zip"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.writeErrorResponse(w, "Invalid request body", "INVALID_REQUEST", http.StatusBadRequest)
		return
	}

	if request.Address == "" || request.City == "" || request.State == "" {
		h.writeErrorResponse(w, "Address, city, and state are required", "MISSING_PARAMETER", http.StatusBadRequest)
		return
	}

	response, err := h.client.SkipTrace(request.FirstName, request.LastName, request.Address, request.City, request.State, request.Zip)
	if err != nil {
		log.Printf("SkipTrace API error: %v", err)
		h.writeErrorResponse(w, "Failed to get skip trace data", "API_ERROR", http.StatusInternalServerError)
		return
	}

	h.writeJSONResponse(w, response)
}
