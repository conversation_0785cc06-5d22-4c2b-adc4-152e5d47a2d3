#!/bin/bash

# =============================================================================
# PropBolt Brain API Testing Script - Real Data Verification
# =============================================================================
# This script tests all API endpoints to ensure they return real data
# and no mock/placeholder data is being used.
# =============================================================================

set -e

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8080}"
REAL_ESTATE_API_KEY="${REAL_ESTATE_API_KEY:-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

echo -e "${BLUE}=== PropBolt Brain API Real Data Testing ===${NC}"
echo "Testing API Base URL: $API_BASE_URL"
echo ""

# Function to test API endpoint
test_endpoint() {
    local endpoint="$1"
    local method="${2:-GET}"
    local data="$3"
    local description="$4"
    
    echo -n "Testing $description... "
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE_URL$endpoint" 2>/dev/null || echo -e "\n000")
    else
        response=$(curl -s -w "\n%{http_code}" "$API_BASE_URL$endpoint" 2>/dev/null || echo -e "\n000")
    fi
    
    http_code=$(echo "$response" | tail -1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        # Check if response contains real data (not mock/placeholder)
        if echo "$body" | grep -q -E "(prop-[0-9]+|mock|placeholder|example|test)" && \
           ! echo "$body" | grep -q -E "(property_id|zpid|address.*FL|Daytona)"; then
            echo -e "${YELLOW}⚠️  MOCK DATA DETECTED${NC}"
            echo "   Response contains mock/placeholder data"
            ((TESTS_FAILED++))
        elif [ -n "$body" ] && [ "$body" != "null" ] && [ "$body" != "{}" ]; then
            echo -e "${GREEN}✅ PASS${NC}"
            ((TESTS_PASSED++))
        else
            echo -e "${RED}❌ EMPTY RESPONSE${NC}"
            ((TESTS_FAILED++))
        fi
    else
        echo -e "${RED}❌ FAIL (HTTP $http_code)${NC}"
        if [ "$http_code" = "000" ]; then
            echo "   Connection failed - is the server running?"
        fi
        ((TESTS_FAILED++))
    fi
}

# Test health endpoint
echo -e "\n${BLUE}=== Testing Health Endpoints ===${NC}"
test_endpoint "/" "GET" "" "Health check"

# Test property search endpoints
echo -e "\n${BLUE}=== Testing Property Search Endpoints ===${NC}"
test_endpoint "/api/v1/proxy/property-search" "POST" '{
    "query": {
        "location": {
            "city": "Daytona Beach",
            "state": "FL"
        },
        "property_type": "Vacant Land",
        "status": "for_sale"
    },
    "limit": 5,
    "offset": 0
}' "Property search with real data"

# Test autocomplete endpoint
echo -e "\n${BLUE}=== Testing Autocomplete Endpoints ===${NC}"
test_endpoint "/api/v1/proxy/autocomplete" "POST" '{
    "query": "123 Main St, Daytona Beach, FL"
}' "Address autocomplete"

# Test property detail endpoint
echo -e "\n${BLUE}=== Testing Property Detail Endpoints ===${NC}"
test_endpoint "/api/v1/proxy/property-detail" "POST" '{
    "address": "123 Main St, Daytona Beach, FL"
}' "Property details by address"

# Test mapping endpoint
echo -e "\n${BLUE}=== Testing Mapping Endpoints ===${NC}"
test_endpoint "/api/v1/proxy/mapping" "POST" '{
    "query": {
        "location": {
            "city": "Daytona Beach",
            "state": "FL"
        },
        "property_type": "Vacant Land"
    }
}' "Property mapping/pins"

# Test legacy endpoints (should still work)
echo -e "\n${BLUE}=== Testing Legacy Endpoints ===${NC}"
test_endpoint "/search/for-sale?neLat=29.3&neLong=-80.9&swLat=29.1&swLong=-81.1&page=1&zoom=12" "GET" "" "Legacy property search"

# Summary
echo -e "\n${BLUE}=== Test Summary ===${NC}"
echo "Tests passed: $TESTS_PASSED"
echo "Tests failed: $TESTS_FAILED"
echo "Total tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED - NO MOCK DATA DETECTED!${NC}"
    echo ""
    echo "✅ All APIs are returning real data"
    echo "✅ No mock/placeholder data found"
    echo "✅ API integration is working properly"
    echo ""
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed or mock data detected.${NC}"
    echo ""
    echo "Common issues:"
    echo "1. Backend server not running (start with: go run main.go)"
    echo "2. Environment variables not set (REAL_ESTATE_API_KEY)"
    echo "3. Mock data still present in frontend components"
    echo ""
    exit 1
fi
