'use client';

import Link from 'next/link';
import { format } from 'date-fns';
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  CalendarIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api/apiClient';

// Fetch active searches with real data
const fetchActiveSearches = async () => {
  const searchQueries = [
    {
      name: 'Daytona Beach Vacant Land',
      location: 'Daytona Beach, FL',
      query: { location: { city: "Daytona Beach", state: "FL" }, property_type: "Vacant Land", price_max: 50000 },
      criteria: { priceRange: '$10K - $50K', lotSize: '1-5 acres', zoning: 'Residential' }
    },
    {
      name: 'Commercial Lots - Highway Access',
      location: 'Volusia County, FL',
      query: { location: { state: "FL" }, property_type: "Vacant Land", price_min: 50000, price_max: 200000 },
      criteria: { priceRange: '$50K - $200K', lotSize: '2-10 acres', zoning: 'Commercial' }
    },
    {
      name: 'Waterfront Properties',
      location: 'New Smyrna Beach, FL',
      query: { location: { city: "New Smyrna Beach", state: "FL" }, property_type: "Vacant Land", price_min: 100000 },
      criteria: { priceRange: '$100K+', lotSize: '0.5-3 acres', features: 'Waterfront' }
    }
  ];

  const searches = await Promise.all(
    searchQueries.map(async (search, index) => {
      try {
        const response = await apiClient.post('/api/v1/proxy/property-search', {
          query: search.query,
          limit: 50,
          offset: 0
        });

        return {
          id: index + 1,
          name: search.name,
          location: search.location,
          criteria: search.criteria,
          results: response.data?.total_count || response.data?.properties?.length || 0,
          lastRun: new Date(),
          alerts: index % 2 === 0
        };
      } catch (error) {
        return {
          id: index + 1,
          name: search.name,
          location: search.location,
          criteria: search.criteria,
          results: 0,
          lastRun: new Date(),
          alerts: false
        };
      }
    })
  );

  return searches;
};

export function ActiveSearches() {
  const { data: activeSearches = [], isLoading, error } = useQuery({
    queryKey: ['active-searches'],
    queryFn: fetchActiveSearches,
    staleTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Active Searches</h3>
            <p className="text-sm text-gray-500">Your saved search parameters</p>
          </div>
          <Link
            href="/search"
            className="btn-primary"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Search
          </Link>
        </div>
      </div>
      <div className="card-body">
        <div className="space-y-4">
          {activeSearches.map((search) => (
            <div
              key={search.id}
              className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors duration-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <MagnifyingGlassIcon className="h-5 w-5 text-primary-500" />
                    <h4 className="text-base font-medium text-gray-900">{search.name}</h4>
                    {search.alerts && (
                      <span className="badge-success">Alerts On</span>
                    )}
                  </div>
                  
                  <div className="mt-2 flex items-center text-sm text-gray-600">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    {search.location}
                  </div>
                  
                  <div className="mt-3 grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Price:</span>
                      <span className="ml-1 font-medium">{search.criteria.priceRange}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Size:</span>
                      <span className="ml-1 font-medium">{search.criteria.lotSize}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Zoning:</span>
                      <span className="ml-1 font-medium">{search.criteria.zoning || search.criteria.features}</span>
                    </div>
                  </div>
                  
                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      Last run: {format(search.lastRun, 'MMM d, h:mm a')}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium text-primary-600">{search.results} results</span>
                    </div>
                  </div>
                </div>
                
                <div className="ml-4 flex items-center space-x-2">
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="View results"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="Edit search"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100"
                    title="Delete search"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {activeSearches.length === 0 && (
          <div className="text-center py-8">
            <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-sm font-medium text-gray-900 mb-2">No active searches</h3>
            <p className="text-sm text-gray-500 mb-4">
              Create your first search to start monitoring properties
            </p>
            <Link href="/search" className="btn-primary">
              Create Search
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
