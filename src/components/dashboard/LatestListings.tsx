'use client';

import Link from 'next/link';
import Image from 'next/image';
import { format } from 'date-fns';
import {
  MapPinIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  EyeIcon,
  BookmarkIcon,
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';
import { useState } from 'react';

// Real API data fetching
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api/apiClient';

// Fetch latest listings from RealEstateAPI
const fetchLatestListings = async () => {
  const response = await apiClient.post('/api/v1/proxy/property-search', {
    query: {
      location: {
        city: "Daytona Beach",
        state: "FL"
      },
      property_type: "Vacant Land",
      status: "for_sale"
    },
    limit: 6,
    offset: 0
  });
  return response.data;
};

export function LatestListings() {
  const { data: listingsData, isLoading, error } = useQuery({
    queryKey: ['latest-listings'],
    queryFn: fetchLatestListings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const latestListings = listingsData?.properties || [];
  const [watchedProperties, setWatchedProperties] = useState<Set<number>>(
    new Set(latestListings.filter(p => p.isWatched).map(p => p.id))
  );

  const toggleWatchList = (propertyId: number) => {
    setWatchedProperties(prev => {
      const newSet = new Set(prev);
      if (newSet.has(propertyId)) {
        newSet.delete(propertyId);
      } else {
        newSet.add(propertyId);
      }
      return newSet;
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Latest Listings</h3>
          <p className="text-sm text-gray-500">Recently added vacant land properties</p>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse p-4 border border-gray-200 rounded-lg">
                <div className="flex space-x-4">
                  <div className="w-24 h-24 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Latest Listings</h3>
          <p className="text-sm text-gray-500">Recently added vacant land properties</p>
        </div>
        <div className="card-body">
          <div className="text-center py-8">
            <div className="text-red-400 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">Unable to load listings</h3>
            <p className="text-sm text-gray-500">Please check your connection and try again</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Latest Listings</h3>
            <p className="text-sm text-gray-500">Recently added vacant land properties</p>
          </div>
          <Link
            href="/search?sort=newest"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all →
          </Link>
        </div>
      </div>
      <div className="card-body">
        <div className="space-y-6">
          {latestListings.map((property) => (
            <div
              key={property.id}
              className="flex space-x-4 p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors duration-200"
            >
              {/* Property Image */}
              <div className="flex-shrink-0">
                <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                  <Image
                    src={property.images[0]}
                    alt={property.address}
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Property Details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-base font-medium text-gray-900 truncate">
                      {property.address}
                    </h4>
                    <div className="mt-1 flex items-center text-sm text-gray-500">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      MLS# {property.mlsNumber}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => toggleWatchList(property.id)}
                      className="p-2 text-gray-400 hover:text-primary-600 rounded-lg hover:bg-gray-100"
                      title={watchedProperties.has(property.id) ? 'Remove from watch list' : 'Add to watch list'}
                    >
                      {watchedProperties.has(property.id) ? (
                        <BookmarkSolidIcon className="h-5 w-5 text-primary-600" />
                      ) : (
                        <BookmarkIcon className="h-5 w-5" />
                      )}
                    </button>
                    <Link
                      href={`/property/${property.id}`}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title="View details"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </Link>
                  </div>
                </div>

                {/* Price and Size */}
                <div className="mt-3 flex items-center space-x-6">
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-lg font-bold text-gray-900">
                      {formatPrice(property.price)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {property.lotSize} acres
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatPrice(property.pricePerAcre)}/acre
                  </div>
                </div>

                {/* Features and Zoning */}
                <div className="mt-2 flex items-center space-x-4">
                  <span className="badge-info">{property.zoning}</span>
                  {property.features.slice(0, 2).map((feature) => (
                    <span key={feature} className="badge-gray">
                      {feature}
                    </span>
                  ))}
                  {property.features.length > 2 && (
                    <span className="text-xs text-gray-500">
                      +{property.features.length - 2} more
                    </span>
                  )}
                </div>

                {/* Listing Date */}
                <div className="mt-2 flex items-center text-xs text-gray-500">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Listed {format(property.listingDate, 'MMM d, yyyy')} • {property.daysOnMarket} days on market
                </div>
              </div>
            </div>
          ))}
        </div>

        {latestListings.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-2">No new listings</h3>
            <p className="text-sm text-gray-500">
              Check back later for new property listings
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
