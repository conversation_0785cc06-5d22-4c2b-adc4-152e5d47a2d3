'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import Map, { Marker, Popup, Source, Layer } from 'react-map-gl';
import type { MapRef } from 'react-map-gl';
import { MapPinIcon } from '@heroicons/react/24/solid';
import { clsx } from 'clsx';

// Mapbox access token - should be set in environment variables
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

interface Property {
  id: string;
  address: string;
  price: number;
  lotSize: number;
  coordinates: [number, number]; // [longitude, latitude]
  zoning: string;
  features: string[];
  images?: string[];
  description?: string;
  pricePerAcre?: number;
  taxInfo?: any;
  ownerInfo?: any;
}

interface MapViewProps {
  properties: Property[];
  selectedProperty: Property | null;
  onPropertySelect: (property: Property | null) => void;
  mapStyle: string;
  isLoading: boolean;
  error: any;
}

// Map style configurations
const MAP_STYLES = {
  satellite: 'mapbox://styles/mapbox/satellite-v9',
  streets: 'mapbox://styles/mapbox/streets-v12',
  outdoors: 'mapbox://styles/mapbox/outdoors-v12',
  light: 'mapbox://styles/mapbox/light-v11',
  dark: 'mapbox://styles/mapbox/dark-v11',
};

// Default map center (Daytona Beach, FL)
const DEFAULT_CENTER = {
  longitude: -81.0228,
  latitude: 29.2108,
  zoom: 11,
};

export function MapView({
  properties,
  selectedProperty,
  onPropertySelect,
  mapStyle,
  isLoading,
  error,
}: MapViewProps) {
  const mapRef = useRef<MapRef>(null);
  const [viewState, setViewState] = useState(DEFAULT_CENTER);
  const [hoveredProperty, setHoveredProperty] = useState<Property | null>(null);

  // Convert properties to GeoJSON format
  const propertiesGeoJSON = {
    type: 'FeatureCollection' as const,
    features: properties.map((property) => ({
      type: 'Feature' as const,
      properties: {
        id: property.id,
        address: property.address,
        price: property.price,
        lotSize: property.lotSize,
        zoning: property.zoning,
        pricePerAcre: property.pricePerAcre,
      },
      geometry: {
        type: 'Point' as const,
        coordinates: property.coordinates,
      },
    })),
  };

  // Fit map to show all properties
  const fitToProperties = useCallback(() => {
    if (properties.length > 0 && mapRef.current) {
      const coordinates = properties.map(p => p.coordinates);
      
      // Calculate bounds
      const lngs = coordinates.map(coord => coord[0]);
      const lats = coordinates.map(coord => coord[1]);
      
      const minLng = Math.min(...lngs);
      const maxLng = Math.max(...lngs);
      const minLat = Math.min(...lats);
      const maxLat = Math.max(...lats);

      mapRef.current.fitBounds(
        [[minLng, minLat], [maxLng, maxLat]],
        { padding: 50, duration: 1000 }
      );
    }
  }, [properties]);

  // Handle property click
  const handlePropertyClick = useCallback((property: Property) => {
    onPropertySelect(property);
    
    // Center map on selected property
    if (mapRef.current) {
      mapRef.current.flyTo({
        center: property.coordinates,
        zoom: 15,
        duration: 1000,
      });
    }
  }, [onPropertySelect]);

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get marker color based on price range
  const getMarkerColor = (price: number) => {
    if (price < 25000) return '#22c55e'; // Green - Low price
    if (price < 75000) return '#f59e0b'; // Yellow - Medium price
    if (price < 150000) return '#f97316'; // Orange - High price
    return '#ef4444'; // Red - Very high price
  };

  if (!MAPBOX_TOKEN) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="text-center">
          <p className="text-gray-600 mb-2">Map unavailable</p>
          <p className="text-sm text-gray-500">Mapbox token not configured</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="text-center">
          <p className="text-red-600 mb-2">Error loading map data</p>
          <p className="text-sm text-gray-500">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full relative rounded-lg overflow-hidden shadow-lg">
      <Map
        ref={mapRef}
        {...viewState}
        onMove={evt => setViewState(evt.viewState)}
        mapboxAccessToken={MAPBOX_TOKEN}
        style={{ width: '100%', height: '100%' }}
        mapStyle={MAP_STYLES[mapStyle as keyof typeof MAP_STYLES] || MAP_STYLES.satellite}
        interactiveLayerIds={['property-clusters']}
      >
        {/* Property markers */}
        {properties.map((property) => (
          <Marker
            key={property.id}
            longitude={property.coordinates[0]}
            latitude={property.coordinates[1]}
            anchor="bottom"
            onClick={(e) => {
              e.originalEvent.stopPropagation();
              handlePropertyClick(property);
            }}
          >
            <div
              className={clsx(
                'relative cursor-pointer transform transition-transform duration-200 hover:scale-110',
                selectedProperty?.id === property.id && 'scale-125 z-10'
              )}
              onMouseEnter={() => setHoveredProperty(property)}
              onMouseLeave={() => setHoveredProperty(null)}
            >
              <div
                className="w-6 h-6 rounded-full border-2 border-white shadow-lg flex items-center justify-center"
                style={{ backgroundColor: getMarkerColor(property.price) }}
              >
                <MapPinIcon className="w-4 h-4 text-white" />
              </div>
              
              {/* Price label */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity duration-200">
                {formatPrice(property.price)}
              </div>
            </div>
          </Marker>
        ))}

        {/* Hover popup */}
        {hoveredProperty && (
          <Popup
            longitude={hoveredProperty.coordinates[0]}
            latitude={hoveredProperty.coordinates[1]}
            anchor="top"
            onClose={() => setHoveredProperty(null)}
            closeButton={false}
            className="property-popup"
          >
            <div className="p-3 min-w-[200px]">
              <h3 className="font-medium text-gray-900 mb-2">
                {hoveredProperty.address}
              </h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-medium">{formatPrice(hoveredProperty.price)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Size:</span>
                  <span className="font-medium">{hoveredProperty.lotSize} acres</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Zoning:</span>
                  <span className="font-medium">{hoveredProperty.zoning}</span>
                </div>
                {hoveredProperty.pricePerAcre && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Per Acre:</span>
                    <span className="font-medium">{formatPrice(hoveredProperty.pricePerAcre)}</span>
                  </div>
                )}
              </div>
            </div>
          </Popup>
        )}

        {/* GeoJSON source for clustering (future enhancement) */}
        <Source
          id="properties"
          type="geojson"
          data={propertiesGeoJSON}
          cluster={true}
          clusterMaxZoom={14}
          clusterRadius={50}
        >
          <Layer
            id="property-clusters"
            type="circle"
            source="properties"
            filter={['has', 'point_count']}
            paint={{
              'circle-color': [
                'step',
                ['get', 'point_count'],
                '#51bbd6',
                100,
                '#f1f075',
                750,
                '#f28cb1'
              ],
              'circle-radius': [
                'step',
                ['get', 'point_count'],
                20,
                100,
                30,
                750,
                40
              ]
            }}
          />
        </Source>
      </Map>

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="spinner"></div>
            <span className="text-gray-600">Loading properties...</span>
          </div>
        </div>
      )}

      {/* Map controls */}
      <div className="absolute top-4 right-4 space-y-2">
        <button
          onClick={fitToProperties}
          className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors duration-200"
          title="Fit to properties"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          </svg>
        </button>
      </div>

      {/* Property count */}
      <div className="absolute bottom-4 left-4 bg-white shadow-lg rounded-lg px-3 py-2">
        <span className="text-sm font-medium text-gray-900">
          {properties.length} properties
        </span>
      </div>
    </div>
  );
}
