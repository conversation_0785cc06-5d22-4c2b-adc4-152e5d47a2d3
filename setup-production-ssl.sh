#!/bin/bash

# =============================================================================
# Production SSL Certificate Setup for PropBolt Brain
# =============================================================================
# This script sets up SSL certificates for brain.propbolt.com using Google Cloud
# =============================================================================

set -e

# Configuration
PROJECT_ID="gold-braid-458901-v2"
PROJECT_NUMBER="456078002475"
DOMAIN="brain.propbolt.com"
CERT_NAME="propbolt-brain-ssl-cert"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Setting up Production SSL for PropBolt Brain${NC}"
echo "=================================================="

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Google Cloud CLI not found. Please install gcloud first.${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project...${NC}"
sudo gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required APIs...${NC}"
sudo gcloud services enable compute.googleapis.com
sudo gcloud services enable certificatemanager.googleapis.com

# Create managed SSL certificate
echo -e "${YELLOW}🔐 Creating managed SSL certificate...${NC}"
sudo gcloud compute ssl-certificates create $CERT_NAME \
    --domains=$DOMAIN \
    --global

# Check certificate status
echo -e "${YELLOW}📋 Checking certificate status...${NC}"
sudo gcloud compute ssl-certificates describe $CERT_NAME --global

# Create HTTPS load balancer target proxy (if not exists)
echo -e "${YELLOW}🔧 Setting up HTTPS load balancer...${NC}"

# Check if target proxy exists
if sudo gcloud compute target-https-proxies describe propbolt-brain-https-proxy --global &>/dev/null; then
    echo -e "${GREEN}✅ HTTPS proxy already exists${NC}"
    
    # Update the SSL certificate
    echo -e "${YELLOW}🔄 Updating SSL certificate on load balancer...${NC}"
    sudo gcloud compute target-https-proxies update propbolt-brain-https-proxy \
        --ssl-certificates=$CERT_NAME \
        --global
else
    echo -e "${YELLOW}🆕 Creating new HTTPS proxy...${NC}"
    sudo gcloud compute target-https-proxies create propbolt-brain-https-proxy \
        --url-map=propbolt-brain-url-map \
        --ssl-certificates=$CERT_NAME \
        --global
fi

# Create forwarding rule for HTTPS (if not exists)
if ! sudo gcloud compute forwarding-rules describe propbolt-brain-https-forwarding-rule --global &>/dev/null; then
    echo -e "${YELLOW}🔗 Creating HTTPS forwarding rule...${NC}"
    sudo gcloud compute forwarding-rules create propbolt-brain-https-forwarding-rule \
        --target-https-proxy=propbolt-brain-https-proxy \
        --global \
        --ports=443
fi

# Get the load balancer IP
LB_IP=$(sudo gcloud compute forwarding-rules describe propbolt-brain-https-forwarding-rule --global --format="value(IPAddress)")

echo -e "${GREEN}✅ SSL certificate setup completed!${NC}"
echo ""
echo -e "${BLUE}📋 SSL Certificate Information:${NC}"
echo "  🔐 Certificate Name: $CERT_NAME"
echo "  🌐 Domain: $DOMAIN"
echo "  📍 Load Balancer IP: $LB_IP"
echo ""
echo -e "${YELLOW}📝 DNS Configuration Required:${NC}"
echo "  Add this A record to your DNS:"
echo "  $DOMAIN → $LB_IP"
echo ""
echo -e "${YELLOW}⏳ Certificate Provisioning:${NC}"
echo "  • Google-managed SSL certificates can take 10-60 minutes to provision"
echo "  • The certificate will be automatically renewed"
echo "  • Check status with: sudo gcloud compute ssl-certificates describe $CERT_NAME --global"
echo ""
echo -e "${BLUE}🔍 Verification Commands:${NC}"
echo "  • Check certificate: sudo gcloud compute ssl-certificates list"
echo "  • Check load balancer: sudo gcloud compute forwarding-rules list"
echo "  • Test HTTPS: curl -I https://$DOMAIN"
echo ""
echo -e "${GREEN}🎉 Production SSL setup completed!${NC}"
