#!/bin/bash

# =============================================================================
# SSL Certificate Setup for PropBolt Brain
# =============================================================================
# This script sets up SSL certificates for local development
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 Setting up SSL certificates for PropBolt Brain${NC}"
echo "=================================================="

# Create certs directory
mkdir -p certs

# Check if mkcert is installed
if ! command -v mkcert &> /dev/null; then
    echo -e "${YELLOW}⚠️  mkcert not found. Installing mkcert...${NC}"
    
    # Install mkcert based on OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install mkcert
        else
            echo -e "${RED}❌ Homebrew not found. Please install mkcert manually.${NC}"
            echo "Visit: https://github.com/FiloSottile/mkcert#installation"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo -e "${YELLOW}Please install mkcert manually for Linux${NC}"
        echo "Visit: https://github.com/FiloSottile/mkcert#installation"
        exit 1
    else
        echo -e "${RED}❌ Unsupported OS. Please install mkcert manually.${NC}"
        echo "Visit: https://github.com/FiloSottile/mkcert#installation"
        exit 1
    fi
fi

# Install local CA
echo -e "${YELLOW}📋 Installing local CA...${NC}"
mkcert -install

# Generate certificates for local development
echo -e "${YELLOW}🔑 Generating SSL certificates...${NC}"
cd certs

# Generate certificate for localhost and brain.propbolt.com
mkcert localhost 127.0.0.1 ::1 brain.propbolt.com *.propbolt.com

# Rename files for easier reference
mv localhost+4.pem server.crt
mv localhost+4-key.pem server.key

echo -e "${GREEN}✅ SSL certificates generated successfully!${NC}"
echo ""
echo -e "${BLUE}📁 Certificate files:${NC}"
echo "  - certs/server.crt (Certificate)"
echo "  - certs/server.key (Private Key)"
echo ""
echo -e "${YELLOW}🔧 Next steps:${NC}"
echo "1. Update your hosts file to point brain.propbolt.com to localhost:"
echo "   sudo echo '127.0.0.1 brain.propbolt.com' >> /etc/hosts"
echo ""
echo "2. Start the servers with SSL enabled"
echo ""
echo -e "${GREEN}🎉 SSL setup completed!${NC}"
