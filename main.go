package main

import (
    "crypto/tls"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "path/filepath"
    "strconv"
    "propbolt/zestimate"
    "propbolt/details"
    "propbolt/search"
    "propbolt/autocomplete"
    "propbolt/handlers"
    "github.com/gorilla/mux"
    "github.com/rs/cors"
)

/* internal notes
lsof -i :8080
kill -9
git add .
git commit -m ""
git push origin main
Production: GOOS=linux GOARCH=amd64 go build -o propbolt
Local: go build -o propbolt
Local: PORT=8080 ./propbolt
*/

// Handler for the health check endpoint
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// Handler for serving static files
func serveStaticFile(filePath string) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        http.ServeFile(w, r, filepath.Join("public", filePath))
    }
}

// Handler for serving the favicon
func faviconHandler(w http.ResponseWriter, r *http.Request) {
    http.ServeFile(w, r, filepath.Join("public", "favicon.ico"))
}

// New handler for the /property endpoint
func propertyHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    var property details.PropertyInfo
    var err error

    // Check for ID parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }

        property, err = details.FromPropertyID(propertyID, nil)
    } else {
        // Check for URL parameter
        propertyURL := r.URL.Query().Get("url")
        if propertyURL != "" {
            property, err = details.FromPropertyURL(propertyURL, nil)
        } else {
            // Check for Address parameter
            homeAddress := r.URL.Query().Get("address")
            if homeAddress != "" {
                property, err = details.FromHomeAddress(homeAddress, nil)
            } else {
                // If no valid parameters are provided, return an error
                http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
                return
            }
        }
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func rentZestimateHandler(w http.ResponseWriter, r *http.Request) {
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    var compPropStatus *bool
    compPropStatusStr := r.URL.Query().Get("compPropStatus")
    if compPropStatusStr != "" {
        if compPropStatusStr == "true" || compPropStatusStr == "false" {
            val, _ := strconv.ParseBool(compPropStatusStr)
            compPropStatus = &val
        } else {
            http.Error(w, "Invalid compPropStatus parameter", http.StatusBadRequest)
            return
        }
    }

    distanceInMilesStr := r.URL.Query().Get("distanceInMiles")
    var distanceInMiles float64 = 5 // Default value
    if distanceInMilesStr != "" {
        var err error
        distanceInMiles, err = strconv.ParseFloat(distanceInMilesStr, 64)
        if err != nil {
            http.Error(w, "Invalid distanceInMiles parameter", http.StatusBadRequest)
            return
        }
    }

    rentZestimate, err := zestimate.GetRentZestimate(address, compPropStatus, distanceInMiles)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving rent zestimate: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, rentZestimate)
}

func main() {
    // Create router
    r := mux.NewRouter()

    // Initialize proxy handler for RealEstateAPI.com integration
    proxyHandler := handlers.NewProxyHandler()

    // Legacy endpoints (existing PropBolt functionality)
    r.HandleFunc("/property", propertyHandler).Methods("GET")
    r.HandleFunc("/propertyMinimal", propertyMinimalHandler).Methods("GET")
    r.HandleFunc("/propertyImages", propertyImagesHandler).Methods("GET")
    r.HandleFunc("/rentEstimate", rentZestimateHandler).Methods("GET")

    // Search endpoints
    r.HandleFunc("/search/for-sale", searchForSaleHandler).Methods("GET")
    r.HandleFunc("/search/for-sale-enhanced", searchForSaleEnhancedHandler).Methods("GET")
    r.HandleFunc("/search/for-rent", searchForRentHandler).Methods("GET")
    r.HandleFunc("/search/sold", searchSoldHandler).Methods("GET")

    // Enhanced search endpoints with location support
    r.HandleFunc("/search/for-sale-by-location", searchForSaleByLocationHandler).Methods("GET")
    r.HandleFunc("/search/for-rent-by-location", searchForRentByLocationHandler).Methods("GET")
    r.HandleFunc("/search/sold-by-location", searchSoldByLocationHandler).Methods("GET")

    // Autocomplete endpoints
    r.HandleFunc("/autocomplete", autocompleteHandler).Methods("GET")
    r.HandleFunc("/region-details", regionDetailsHandler).Methods("GET")

    // NEW: RealEstateAPI.com Proxy Endpoints (Admin-only)
    api := r.PathPrefix("/api/v1/proxy").Subrouter()
    api.HandleFunc("/autocomplete", proxyHandler.AutoCompleteHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/mapping", proxyHandler.PropertyMappingHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/property-detail", proxyHandler.PropertyDetailHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/property-detail-bulk", proxyHandler.PropertyDetailBulkHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/property-search", proxyHandler.PropertySearchHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/involuntary-liens", proxyHandler.InvoluntaryLiensHandler).Methods("POST", "OPTIONS")
    api.HandleFunc("/property-comps-v3", proxyHandler.PropertyCompsV3Handler).Methods("POST", "OPTIONS")
    api.HandleFunc("/property-comps-v2", proxyHandler.PropertyCompsV2Handler).Methods("POST", "OPTIONS")
    api.HandleFunc("/skiptrace", proxyHandler.SkipTraceHandler).Methods("POST", "OPTIONS")

    // Health Check Endpoint
    r.HandleFunc("/", healthCheckHandler).Methods("GET")

    // Static file endpoints
    r.HandleFunc("/favicon.ico", serveStaticFile("favicon.ico")).Methods("GET")
    r.HandleFunc("/api-logo.png", serveStaticFile("api-logo.png")).Methods("GET")
    r.HandleFunc("/cover-photo-tutorial-one.png", serveStaticFile("cover-photo-tutorial-one.png")).Methods("GET")
    r.HandleFunc("/byte-media-logo-v2.png", serveStaticFile("byte-media-logo-v2.png")).Methods("GET")

    // Serve static files from the 'public' directory
    r.PathPrefix("/public/").Handler(http.StripPrefix("/public/", http.FileServer(http.Dir("public"))))

    // Setup CORS
    c := cors.New(cors.Options{
        AllowedOrigins: []string{"https://brain.propbolt.com", "https://propbolt.com", "http://localhost:3000"},
        AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        AllowedHeaders: []string{"*"},
        AllowCredentials: true,
    })

    // Wrap router with CORS
    handler := c.Handler(r)

    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }

    // Check if SSL certificates exist for HTTPS
    certFile := "certs/server.crt"
    keyFile := "certs/server.key"

    if _, err := os.Stat(certFile); err == nil {
        if _, err := os.Stat(keyFile); err == nil {
            // SSL certificates found, start HTTPS server
            fmt.Printf("PropBolt Brain API Server started with SSL at https://localhost:%s\n", port)
            fmt.Printf("RealEstateAPI.com proxy endpoints available at /api/v1/proxy/*\n")

            // Configure TLS
            tlsConfig := &tls.Config{
                MinVersion: tls.VersionTLS12,
            }

            server := &http.Server{
                Addr:      ":" + port,
                Handler:   handler,
                TLSConfig: tlsConfig,
            }

            log.Fatal(server.ListenAndServeTLS(certFile, keyFile))
        }
    }

    // Fallback to HTTP if no SSL certificates
    fmt.Printf("PropBolt Brain API Server started at http://localhost:%s\n", port)
    fmt.Printf("RealEstateAPI.com proxy endpoints available at /api/v1/proxy/*\n")
    fmt.Printf("⚠️  Running without SSL - use setup-ssl.sh to enable HTTPS\n")
    log.Fatal(http.ListenAndServe(":"+port, handler))
}

func propertyMinimalHandler(w http.ResponseWriter, r *http.Request) {
    // Parse the listingPhotos parameter
    listingPhotosStr := r.URL.Query().Get("listingPhotos")
    listingPhotos := false
    if listingPhotosStr == "true" {
        listingPhotos = true
    }

    propertyIDStr := r.URL.Query().Get("id")
    propertyURL := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.PropertyMinimalInfo
    var err error

    if propertyIDStr != "" {
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDMinimal(propertyID, nil)
    } else if propertyURL != "" {
        property, err = details.FromPropertyURLMinimal(propertyURL, nil)
    } else if homeAddress != "" {
        property, err = details.FromHomeAddressMinimal(homeAddress, nil)
    } else {
        http.Error(w, "Either Property ID, URL, or Address is required", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    if !listingPhotos {
        property.ResponsivePhotos = nil
    }

    writeJSONResponse(w, property)
}

func propertyImagesHandler(w http.ResponseWriter, r *http.Request) {
    propertyIDStr := r.URL.Query().Get("id")
    propertyURL := r.URL.Query().Get("url")
    homeAddress := r.URL.Query().Get("address")

    var property details.ImagesOnly
    var err error

    switch {
    case propertyIDStr != "":
        propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, fmt.Sprintf("Error parsing property ID: %v", err), http.StatusBadRequest)
            return
        }
        property, err = details.FromPropertyIDPhotos(propertyID, nil)
    case propertyURL != "":
        property, err = details.FromPropertyURLPhotos(propertyURL, nil)
    case homeAddress != "":
        property, err = details.FromHomeAddressPhotos(homeAddress, nil)
    default:
        http.Error(w, "Must provide ID, URL, or Address parameter", http.StatusBadRequest)
        return
    }

    if err != nil {
        http.Error(w, fmt.Sprintf("Error retrieving property details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, property)
}


func writeJSONResponse(w http.ResponseWriter, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    rawJSON, err := json.MarshalIndent(data, "", "  ")
    if err != nil {
        http.Error(w, fmt.Sprintf("Error marshalling property details: %v", err), http.StatusInternalServerError)
        return
    }
    w.Write(rawJSON)
}

// Helper function to parse boolean parameters
func parseBoolParam(r *http.Request, param string, defaultValue bool) bool {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue
    }
    result, err := strconv.ParseBool(value)
    if err != nil {
        return defaultValue
    }
    return result
}

// Helper function to parse integer parameters
func parseIntParam(r *http.Request, param string, defaultValue int) (int, error) {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue, nil
    }
    result, err := strconv.Atoi(value)
    if err != nil {
        return defaultValue, fmt.Errorf("invalid %s parameter: %v", param, err)
    }
    return result, nil
}

// Helper function to parse float parameters
func parseFloatParam(r *http.Request, param string, defaultValue float64) (float64, error) {
    value := r.URL.Query().Get(param)
    if value == "" {
        return defaultValue, nil
    }
    result, err := strconv.ParseFloat(value, 64)
    if err != nil {
        return defaultValue, fmt.Errorf("invalid %s parameter: %v", param, err)
    }
    return result, nil
}

// Search response structure
type SearchResponse struct {
    ListResults []search.ListResult `json:"listResults"`
    MapResults  []search.MapResult  `json:"mapResults"`
    TotalCount  int                 `json:"totalCount"`
}

// Handler for /search/for-sale endpoint
func searchForSaleHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Property type filters
    isAllHomes := parseBoolParam(r, "isAllHomes", true)
    isTownhouse := parseBoolParam(r, "isTownhouse", false)
    isMultiFamily := parseBoolParam(r, "isMultiFamily", false)
    isCondo := parseBoolParam(r, "isCondo", false)
    isLotLand := parseBoolParam(r, "isLotLand", false)
    isApartment := parseBoolParam(r, "isApartment", false)
    isManufactured := parseBoolParam(r, "isManufactured", false)
    isApartmentOrCondo := parseBoolParam(r, "isApartmentOrCondo", false)

    // School filters
    isElementarySchool := parseBoolParam(r, "isElementarySchool", false)
    isMiddleSchool := parseBoolParam(r, "isMiddleSchool", false)
    isHighSchool := parseBoolParam(r, "isHighSchool", false)
    isPublicSchool := parseBoolParam(r, "isPublicSchool", false)
    isPrivateSchool := parseBoolParam(r, "isPrivateSchool", false)
    isCharterSchool := parseBoolParam(r, "isCharterSchool", false)
    includeUnratedSchools := parseBoolParam(r, "includeUnratedSchools", false)

    // Price filters
    priceMin, err := parseIntParam(r, "priceMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    priceMax, err := parseIntParam(r, "priceMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMin, err := parseIntParam(r, "monthlyPaymentMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMax, err := parseIntParam(r, "monthlyPaymentMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForSale(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool,
        isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
        isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment,
        isManufactured, isApartmentOrCondo,
        priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/for-rent endpoint
func searchForRentHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForRent(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/sold endpoint
func searchSoldHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.Sold(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /autocomplete endpoint
func autocompleteHandler(w http.ResponseWriter, r *http.Request) {
    query := r.URL.Query().Get("q")
    if query == "" {
        http.Error(w, "Query parameter 'q' is required", http.StatusBadRequest)
        return
    }

    suggestions, err := autocomplete.GetSuggestions(query, nil)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error getting autocomplete suggestions: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, suggestions)
}

// Handler for /region-details endpoint
func regionDetailsHandler(w http.ResponseWriter, r *http.Request) {
    regionIDStr := r.URL.Query().Get("regionId")
    if regionIDStr == "" {
        http.Error(w, "regionId parameter is required", http.StatusBadRequest)
        return
    }

    regionID, err := strconv.Atoi(regionIDStr)
    if err != nil {
        http.Error(w, "Invalid regionId parameter", http.StatusBadRequest)
        return
    }

    details, err := autocomplete.GetRegionDetails(regionID, nil)
    if err != nil {
        http.Error(w, fmt.Sprintf("Error getting region details: %v", err), http.StatusInternalServerError)
        return
    }

    writeJSONResponse(w, details)
}

// Handler for /search/for-sale-by-location endpoint
func searchForSaleByLocationHandler(w http.ResponseWriter, r *http.Request) {
    // Get location parameters
    address := r.URL.Query().Get("address")
    propertyURL := r.URL.Query().Get("url")
    propertyIDStr := r.URL.Query().Get("id")

    var propertyID int64
    if propertyIDStr != "" {
        var err error
        propertyID, err = strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, "Invalid property ID parameter", http.StatusBadRequest)
            return
        }
    }

    // Validate that at least one location parameter is provided
    if address == "" && propertyURL == "" && propertyID == 0 {
        http.Error(w, "Must provide address, url, or id parameter", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Property type filters
    isAllHomes := parseBoolParam(r, "isAllHomes", true)
    isTownhouse := parseBoolParam(r, "isTownhouse", false)
    isMultiFamily := parseBoolParam(r, "isMultiFamily", false)
    isCondo := parseBoolParam(r, "isCondo", false)
    isLotLand := parseBoolParam(r, "isLotLand", false)
    isApartment := parseBoolParam(r, "isApartment", false)
    isManufactured := parseBoolParam(r, "isManufactured", false)
    isApartmentOrCondo := parseBoolParam(r, "isApartmentOrCondo", false)

    // School filters
    isElementarySchool := parseBoolParam(r, "isElementarySchool", false)
    isMiddleSchool := parseBoolParam(r, "isMiddleSchool", false)
    isHighSchool := parseBoolParam(r, "isHighSchool", false)
    isPublicSchool := parseBoolParam(r, "isPublicSchool", false)
    isPrivateSchool := parseBoolParam(r, "isPrivateSchool", false)
    isCharterSchool := parseBoolParam(r, "isCharterSchool", false)
    includeUnratedSchools := parseBoolParam(r, "includeUnratedSchools", false)

    // Price filters
    priceMin, err := parseIntParam(r, "priceMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    priceMax, err := parseIntParam(r, "priceMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMin, err := parseIntParam(r, "monthlyPaymentMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMax, err := parseIntParam(r, "monthlyPaymentMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForSaleByLocation(
        pagination, zoomValue, address, propertyURL, propertyID,
        isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool,
        isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
        isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment,
        isManufactured, isApartmentOrCondo,
        priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/for-rent-by-location endpoint
func searchForRentByLocationHandler(w http.ResponseWriter, r *http.Request) {
    // Get location parameters
    address := r.URL.Query().Get("address")
    propertyURL := r.URL.Query().Get("url")
    propertyIDStr := r.URL.Query().Get("id")

    var propertyID int64
    if propertyIDStr != "" {
        var err error
        propertyID, err = strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, "Invalid property ID parameter", http.StatusBadRequest)
            return
        }
    }

    // Validate that at least one location parameter is provided
    if address == "" && propertyURL == "" && propertyID == 0 {
        http.Error(w, "Must provide address, url, or id parameter", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.ForRentByLocation(
        pagination, zoomValue, address, propertyURL, propertyID,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/sold-by-location endpoint
func searchSoldByLocationHandler(w http.ResponseWriter, r *http.Request) {
    // Get location parameters
    address := r.URL.Query().Get("address")
    propertyURL := r.URL.Query().Get("url")
    propertyIDStr := r.URL.Query().Get("id")

    var propertyID int64
    if propertyIDStr != "" {
        var err error
        propertyID, err = strconv.ParseInt(propertyIDStr, 10, 64)
        if err != nil {
            http.Error(w, "Invalid property ID parameter", http.StatusBadRequest)
            return
        }
    }

    // Validate that at least one location parameter is provided
    if address == "" && propertyURL == "" && propertyID == 0 {
        http.Error(w, "Must provide address, url, or id parameter", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Call the search function
    listResults, mapResults, err := search.SoldByLocation(
        pagination, zoomValue, address, propertyURL, propertyID,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}

// Handler for /search/for-sale-enhanced endpoint with land-specific filters
func searchForSaleEnhancedHandler(w http.ResponseWriter, r *http.Request) {
    // Required geographic bounds
    neLat, err := parseFloatParam(r, "neLat", 0)
    if err != nil || neLat == 0 {
        http.Error(w, "neLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    neLong, err := parseFloatParam(r, "neLong", 0)
    if err != nil || neLong == 0 {
        http.Error(w, "neLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLat, err := parseFloatParam(r, "swLat", 0)
    if err != nil || swLat == 0 {
        http.Error(w, "swLat parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    swLong, err := parseFloatParam(r, "swLong", 0)
    if err != nil || swLong == 0 {
        http.Error(w, "swLong parameter is required and must be a valid number", http.StatusBadRequest)
        return
    }

    // Optional parameters with defaults
    pagination, err := parseIntParam(r, "page", 1)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    zoomValue, err := parseIntParam(r, "zoom", 10)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    if zoomValue < 1 || zoomValue > 20 {
        http.Error(w, "zoom parameter must be between 1 and 20", http.StatusBadRequest)
        return
    }

    // Property type filters
    isAllHomes := parseBoolParam(r, "isAllHomes", true)
    isTownhouse := parseBoolParam(r, "isTownhouse", false)
    isMultiFamily := parseBoolParam(r, "isMultiFamily", false)
    isCondo := parseBoolParam(r, "isCondo", false)
    isLotLand := parseBoolParam(r, "isLotLand", false)
    isApartment := parseBoolParam(r, "isApartment", false)
    isManufactured := parseBoolParam(r, "isManufactured", false)
    isApartmentOrCondo := parseBoolParam(r, "isApartmentOrCondo", false)

    // School filters
    isElementarySchool := parseBoolParam(r, "isElementarySchool", false)
    isMiddleSchool := parseBoolParam(r, "isMiddleSchool", false)
    isHighSchool := parseBoolParam(r, "isHighSchool", false)
    isPublicSchool := parseBoolParam(r, "isPublicSchool", false)
    isPrivateSchool := parseBoolParam(r, "isPrivateSchool", false)
    isCharterSchool := parseBoolParam(r, "isCharterSchool", false)
    includeUnratedSchools := parseBoolParam(r, "includeUnratedSchools", false)

    // Price filters
    priceMin, err := parseIntParam(r, "priceMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    priceMax, err := parseIntParam(r, "priceMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMin, err := parseIntParam(r, "monthlyPaymentMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    monthlyPaymentMax, err := parseIntParam(r, "monthlyPaymentMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    // NEW: Land-specific parameters
    lotSizeMin, err := parseIntParam(r, "lotSizeMin", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    lotSizeMax, err := parseIntParam(r, "lotSizeMax", 0)
    if err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    // Validate lot size range
    if lotSizeMin > 0 && lotSizeMax > 0 && lotSizeMin > lotSizeMax {
        http.Error(w, "lotSizeMin cannot be greater than lotSizeMax", http.StatusBadRequest)
        return
    }

    hasUtilities := parseBoolParam(r, "hasUtilities", false)
    hasWater := parseBoolParam(r, "hasWater", false)
    hasSewer := parseBoolParam(r, "hasSewer", false)
    hasElectric := parseBoolParam(r, "hasElectric", false)
    hasGas := parseBoolParam(r, "hasGas", false)
    isWaterfront := parseBoolParam(r, "isWaterfront", false)
    hasView := parseBoolParam(r, "hasView", false)
    isBuildable := parseBoolParam(r, "isBuildable", false)
    hasRoadAccess := parseBoolParam(r, "hasRoadAccess", false)

    zoningType := r.URL.Query().Get("zoningType") // residential, commercial, agricultural

    // Call the enhanced search function
    listResults, mapResults, err := search.ForSaleEnhanced(
        pagination, zoomValue, neLat, neLong, swLat, swLong,
        isAllHomes, isElementarySchool, isMiddleSchool, isHighSchool,
        isPublicSchool, isPrivateSchool, isCharterSchool, includeUnratedSchools,
        isTownhouse, isMultiFamily, isCondo, isLotLand, isApartment,
        isManufactured, isApartmentOrCondo,
        priceMin, priceMax, monthlyPaymentMin, monthlyPaymentMax,
        // Land-specific parameters
        lotSizeMin, lotSizeMax,
        hasUtilities, hasWater, hasSewer, hasElectric, hasGas,
        isWaterfront, hasView, isBuildable, hasRoadAccess,
        zoningType,
        nil, // proxyURL
    )

    if err != nil {
        http.Error(w, fmt.Sprintf("Error performing enhanced search: %v", err), http.StatusInternalServerError)
        return
    }

    response := SearchResponse{
        ListResults: listResults,
        MapResults:  mapResults,
        TotalCount:  len(listResults),
    }

    writeJSONResponse(w, response)
}
